import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Link, useNavigate } from 'react-router-dom';
import {
  BarChart, Calendar, Map, Users, Star, MessageCircle,
  HelpCircle, LogOut, ChevronRight, Loader2,
  Clock, Banknote, Award, Download,
  TrendingUp, Activity, DollarSign, Eye, Ban
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';
import { format, parseISO } from 'date-fns';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { toast } from "react-hot-toast";
import * as XLSX from 'xlsx';
// Excel formatting utilities imported for future enhancement
import {
  getOptimizedDashboardStats,
  getOptimizedPopularCourts,
  getOptimizedCustomRevenue,
  getOptimizedVenuesWithStats,
  logPerformanceImprovement
} from '@/utils/dashboardOptimization';
import { RealTimeDashboardMetrics } from '@/utils/realTimeUpdates';
import RealTimeNotifications from '@/components/admin/RealTimeNotifications';
import { getDisplayName, getDisplayEmail } from '@/utils/security';
import SupportButton from '@/components/SupportButton';
import { NotificationAnalytics } from '@/components/admin/NotificationAnalytics';
import { NotificationTemplateLibrary } from '@/components/admin/NotificationTemplateLibrary';
import VenueSelector from '@/components/admin/VenueSelector';

interface QuickStats {
  todayBookings: number;
  averageRating: number;
  occupancyRate: number;
  isLoading: boolean;
  pendingBookings: number;
  monthlyRevenue: number;
  upcomingBookings: number;
  recentReviews: number;
}

interface VenueWithBookingStats {
  id: string;
  name: string;
  bookings_count: number;
  total_revenue: number;
  platform_fee_percentage: number;
}

interface CourtStats {
  court_name: string;
  bookings_percentage: number;
}

interface WeatherData {
  current: {
    temp: number;
  };
  severe?: any[];
  forecast: Array<{
    time: string;
    temp: number;
    precipitation: number;
    weathercode: number;
  }>;
  daily?: Array<{
    date: string;
    day: string;
    temp_max: number;
    temp_min: number;
    icon: string;
    rain: number;
    summary: string;
  }>;
}

interface BroadcastNotification {
  id: string;
  title: string;
  message: string;
  approved: boolean;
  metadata?: {
    venue_id: string;
  };
}

// WeatherWidget for AdminHome_Mobile
const WeatherWidget: React.FC<{ venueId: string }> = ({ venueId }) => {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    const fetchWeather = async () => {
      setLoading(true);
      setError(null);
      try {
        const sessionResult = await supabase.auth.getSession();
        const jwt = sessionResult.data.session?.access_token;
        if (!jwt) throw new Error('Not authenticated');
        // Fetch both hourly and daily data for 3-day summary and hourly rain
        const weatherUrl = 'https://lrtirloetmulgmdxnusl.supabase.co/functions/v1/weather-proxy';
        const res = await fetch(weatherUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwt}`,
          },
          body: JSON.stringify({ venue_id: venueId, daily: true }),
        });
        const weatherData = await res.json();
        if (!res.ok) throw new Error(weatherData.error || 'Failed to fetch weather');
        setWeather(weatherData);
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };
    if (venueId) fetchWeather();
  }, [venueId]);

  if (!venueId) return null;

  // Helper: Weather Impact Score
  function getImpactScore() {
    if (!weather) return null;
    // Simple logic: if any severe, Poor; if rain > 2mm in next 12h, Moderate; else Good
    if (weather.severe && weather.severe.length > 0) return { label: 'Poor', color: 'bg-red-500' };
    if (weather.forecast && weather.forecast.some((f: any) => f.precipitation > 2)) return { label: 'Moderate', color: 'bg-yellow-500' };
    return { label: 'Good', color: 'bg-emerald-500' };
  }
  const impact = getImpactScore();

  return (
    <Card className="cursor-pointer text-xs rounded-xl border border-emerald-200/20 bg-white/5 backdrop-blur-sm" onClick={() => setExpanded(e => !e)}>
      <CardHeader className="pb-1 flex flex-row items-center justify-between">
        <CardTitle className="text-base text-emerald-700">Weather Forecast</CardTitle>
        {impact && (
          <span className={`text-xs px-2 py-1 rounded ${impact.color} text-white`}>{impact.label} for outdoor play</span>
        )}
      </CardHeader>
      <CardContent className="pt-0 px-2">
        {loading && <div className="text-xs text-gray-600">Loading weather...</div>}
        {error && <div className="text-xs text-red-600">{error}</div>}
        {weather && (
          <>
            {/* Current Weather */}
            <div className="flex items-center gap-2 mb-1 ml-4">
              <span className="text-xl font-bold text-emerald-700">{Math.round(weather.current.temp)}°C</span>
              <span className="text-xs text-gray-600">Now</span>
              {weather.severe && weather.severe.length > 0 && (
                <Badge variant="destructive">Severe Weather</Badge>
              )}
            </div>
            {/* 3-Day Compact Forecast */}
            {weather.daily && (
              <div className="flex gap-1 mb-1">
                {weather.daily.slice(0, 3).map((d: any, i: number) => (
                  <div key={d.date} className="flex flex-col items-center min-w-[48px]">
                    <span className="text-[10px] text-gray-600 font-medium">{d.day}</span>
                    <span className="text-base font-bold text-emerald-700">{Math.round(d.temp_max)}°</span>
                    <span className="text-[10px] text-gray-500">{Math.round(d.temp_min)}°</span>
                    <span className="text-xs">{d.icon}</span>
                    {d.rain > 0 && <span className="text-blue-600 text-[10px]">{d.rain}mm</span>}
                  </div>
                ))}
              </div>
            )}
            {/* Hourly Rain/Storm Timeline (next 12h) */}
            <div className="flex gap-1 overflow-x-auto pb-1">
              {weather.forecast.slice(0, 12).map((f: any, i: number) => (
                <div key={f.time} className="flex flex-col items-center min-w-[32px]">
                  <span className="text-[10px] text-gray-600">{new Date(f.time).getHours()}:00</span>
                  <span className="font-medium text-xs text-emerald-700">{Math.round(f.temp)}°</span>
                  {f.precipitation > 0 && (
                    <span className="text-blue-600 text-[10px]">{f.precipitation}mm</span>
                  )}
                  {[95,96,99].includes(f.weathercode) && (
                    <span className="text-red-600 text-[10px]">Storm</span>
                  )}
                </div>
              ))}
            </div>
            {/* Expandable details */}
            {expanded && weather.daily && (
              <div className="mt-2">
                <div className="text-xs text-gray-500 mb-1">3-Day Details</div>
                <div className="flex gap-2">
                  {weather.daily.slice(0, 3).map((d: any, i: number) => (
                    <div key={d.date} className="flex flex-col items-center min-w-[80px] p-2 rounded bg-emerald-100 text-emerald-800">
                      <span className="text-xs font-medium">{d.day}</span>
                      <span className="text-lg font-bold">{Math.round(d.temp_max)}° / {Math.round(d.temp_min)}°</span>
                      <span className="text-xs">{d.icon}</span>
                      <span className="text-xs text-blue-700">Rain: {d.rain}mm</span>
                      <span className="text-xs text-emerald-600">{d.summary}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

const AdminHome_Mobile = () => {
  const { user, userRole, signOut } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [scrolled, setScrolled] = useState(false);
  const [stats, setStats] = useState<QuickStats>({
    todayBookings: 0,
    averageRating: 0,
    occupancyRate: 0,
    isLoading: true,
    pendingBookings: 0,
    monthlyRevenue: 0,
    upcomingBookings: 0,
    recentReviews: 0
  });
  const [adminVenues, setAdminVenues] = useState<Array<{ venue_id: string }>>([]);
  const [userRoleState, setUserRoleState] = useState<string | null>(null);

  const [customStartDate, setCustomStartDate] = useState<Date | null>(null);
  const [customEndDate, setCustomEndDate] = useState<Date | null>(null);
  const [todaysRevenue, setTodaysRevenue] = useState(0);
  const [customRevenue, setCustomRevenue] = useState(0);
  // PHASE 2: Online vs Offline Revenue Tracking
  const [onlineRevenue, setOnlineRevenue] = useState<number>(0);
  const [offlineRevenue, setOfflineRevenue] = useState<number>(0);
  const [onlineBookings, setOnlineBookings] = useState<number>(0);
  const [offlineBookings, setOfflineBookings] = useState<number>(0);
  // FIX: Add Net Settlement tracking for correct display
  const [onlineNetRevenue, setOnlineNetRevenue] = useState<number>(0);
  // TDS: Add TDS tracking for display
  const [onlineTdsAmount, setOnlineTdsAmount] = useState<number>(0);
  const [totalTdsAmount, setTotalTdsAmount] = useState<number>(0);
  const [selectedVenueForRevenue, setSelectedVenueForRevenue] = useState<string>('all');
  const [pendingVenueSelection, setPendingVenueSelection] = useState<string>('all');
  const [showVenueConfirmation, setShowVenueConfirmation] = useState<boolean>(false);

  // Custom venue change handler with confirmation
  const handleVenueChange = (newVenueId: string) => {
    setPendingVenueSelection(newVenueId);
    if (selectedVenueForRevenue === 'all' && newVenueId !== 'all') {
      setShowVenueConfirmation(true);
    } else if (newVenueId === 'all' || newVenueId === selectedVenueForRevenue) {
      setSelectedVenueForRevenue(newVenueId);
      setShowVenueConfirmation(false);
    } else {
      setShowVenueConfirmation(true);
    }
  };

  // Confirm venue selection
  const confirmVenueSelection = () => {
    setSelectedVenueForRevenue(pendingVenueSelection);
    setShowVenueConfirmation(false);
  };
  const [venues, setVenues] = useState<Array<{ id: string; name: string }>>([]);
  const [popularCourts, setPopularCourts] = useState<CourtStats[]>([]);
  const [courtDataLoading, setCourtDataLoading] = useState(true);
  const [venuesWithStats, setVenuesWithStats] = useState<VenueWithBookingStats[]>([]);
  const [venueSubscribers, setVenueSubscribers] = useState<Record<string, number>>({});
  const [broadcastModal, setBroadcastModal] = useState<{ open: boolean, venueId: string | null }>({ open: false, venueId: null });
  const [broadcastTitle, setBroadcastTitle] = useState('');
  const [broadcastMessage, setBroadcastMessage] = useState('');
  const [broadcastLoading, setBroadcastLoading] = useState(false);
  const approvedBroadcastIdsRef = useRef<Set<string>>(new Set());
  const [broadcastHistory, setBroadcastHistory] = useState<Record<string, any[]>>({});
  const [downloadingReport, setDownloadingReport] = useState(false);

  // 🔄 REAL-TIME UPDATES: State for live dashboard updates
  const [showRealTimeUpdates, setShowRealTimeUpdates] = useState(true);

  // Scroll handler for header effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // If not on mobile, redirect to desktop admin
  useEffect(() => {
    if (!isMobile) {
      navigate('/admin');
    }
  }, [isMobile, navigate]);

  // Fetch admin venues first
  useEffect(() => {
    const fetchAdminVenues = async () => {
      try {
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        if (!currentUser) return;

        // Get user role from user_roles table instead of profiles
        const { data: userRoles, error: roleError } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', currentUser.id); // Remove .single() to handle multiple roles

        if (roleError) {
          console.error('Error fetching user role:', roleError);
          return;
        }

        // Handle multiple roles - find the highest role (super_admin > admin > user)
        let highestRole = null;
        if (userRoles && userRoles.length > 0) {
          if (userRoles.some(r => r.role === 'super_admin')) {
            highestRole = 'super_admin';
          } else if (userRoles.some(r => r.role === 'admin')) {
            highestRole = 'admin';
          } else {
            highestRole = 'user';
          }
        }

        setUserRoleState(highestRole);

        if (highestRole === 'admin') {
          // If admin, get their venues
          const { data: venues } = await supabase.rpc('get_admin_venues');
          setAdminVenues(venues || []);
        } else if (highestRole === 'super_admin') {
          // Super admin has access to all venues, so we leave adminVenues empty
          setAdminVenues([]);
        }
      } catch (error) {
        console.error('Error fetching admin venues:', error);
      }
    };
    fetchAdminVenues();
  }, []);

  // ⚡ OPTIMIZED: Fetch venues with stats using optimized function
  useEffect(() => {
    const fetchVenuesWithStats = async () => {
      if (!user?.id || !userRoleState) return;

      try {
        // ✅ PERFORMANCE OPTIMIZATION: Single query replaces N+1 venue queries
        const venuesWithData = await getOptimizedVenuesWithStats(
          user.id,
          userRoleState,
          adminVenues
        );

        setVenuesWithStats(venuesWithData);
      } catch (error) {
        console.error('Error fetching optimized venues with stats:', error);
      }
    };

    if (adminVenues.length > 0 || userRoleState === 'super_admin') {
      fetchVenuesWithStats();
    }
  }, [adminVenues, userRoleState, user?.id]);

  // Fetch venues for revenue analytics selector
  useEffect(() => {
    const fetchVenues = async () => {
      try {
        const { data: venuesData, error } = await supabase
          .from('venues')
          .select('id, name')
          .eq('is_active', true)
          .order('name');

        if (error) throw error;
        setVenues(venuesData || []);
      } catch (error) {
        console.error('Error fetching venues:', error);
      }
    };

    fetchVenues();
  }, []);

  // Set default venue selection for revenue analytics
  useEffect(() => {
    if (userRole === 'admin' && adminVenues.length > 0) {
      setSelectedVenueForRevenue(adminVenues[0].venue_id);
    }
  }, [userRole, adminVenues]);

  // ⚡ OPTIMIZED: Fetch dashboard metrics with single query
  useEffect(() => {
    const fetchDashboardMetrics = async () => {
      if (!user?.id || !userRoleState) return;

      try {
        // Set loading state
        setStats(prev => ({ ...prev, isLoading: true }));
        setCourtDataLoading(true);

        // ✅ PERFORMANCE OPTIMIZATION: Single query replaces 8+ separate queries
        const dashboardStats = await getOptimizedDashboardStats(user.id);

        // ✅ PERFORMANCE OPTIMIZATION: Single query for popular courts
        const popularCourtsData = await getOptimizedPopularCourts(user.id, 30);

        // ✅ OPTIMIZED: All booking stats fetched in single server-side query
        // Previous approach: 3 separate booking queries + client-side filtering
        // New approach: 1 server-side function with all stats

        // ✅ OPTIMIZED: Rating and review stats included in single dashboard query
        // Previous approach: 2 separate review queries + client-side calculations
        // New approach: Server-side aggregation in dashboard stats function

        // ✅ OPTIMIZED: Today's revenue included in dashboard stats
        // Previous approach: Separate revenue query + client-side filtering
        // New approach: Server-side revenue calculation in dashboard function
        setTodaysRevenue(dashboardStats.todaysRevenue);

        // ✅ OPTIMIZED: Popular courts with single server-side query
        // Previous approach: N+1 queries (1 for courts + N for each court's bookings)
        // New approach: Single server-side function with aggregated stats
        setPopularCourts(popularCourtsData);
        setCourtDataLoading(false);

        // ✅ OPTIMIZED: All stats calculated server-side in single function
        // Previous approach: Multiple separate queries + client-side calculations
        // New approach: Server-side aggregation with optimized performance

        // Update state with optimized data
        setStats({
          todayBookings: dashboardStats.todayBookings,
          averageRating: dashboardStats.averageRating,
          occupancyRate: dashboardStats.occupancyRate,
          isLoading: false,
          pendingBookings: dashboardStats.pendingBookings,
          monthlyRevenue: dashboardStats.todaysRevenue,
          upcomingBookings: dashboardStats.upcomingBookings,
          recentReviews: dashboardStats.recentReviews
        });
      } catch (error) {
        console.error('Error fetching dashboard metrics:', error);
        setStats(prev => ({ ...prev, isLoading: false }));
      }
    };

    fetchDashboardMetrics();
    
    // Set up realtime subscription for bookings table to refresh data when changes occur
    const bookingsChannel = supabase.channel('public:bookings')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'bookings' }, 
        () => {
          // Refresh metrics when bookings change
          fetchDashboardMetrics();
        }
      )
      .subscribe();

    // Set up realtime subscription for reviews
    const reviewsChannel = supabase.channel('public:reviews')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'reviews' }, 
        () => {
          // Refresh metrics when reviews change
          fetchDashboardMetrics();
        }
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(bookingsChannel);
      supabase.removeChannel(reviewsChannel);
    };
  }, [user?.id, userRoleState]); // ✅ OPTIMIZED: Simplified dependencies

  // Fetch subscriber counts for each admin venue
  useEffect(() => {
    const fetchSubscribers = async () => {
      if (!adminVenues.length) return;
      const counts: Record<string, number> = {};
      for (const v of adminVenues) {
        const { count } = await supabase
          .from('venue_subscriptions')
          .select('id', { count: 'exact' })
          .eq('venue_id', v.venue_id);
        counts[v.venue_id] = count || 0;
      }
      setVenueSubscribers(counts);
    };
    fetchSubscribers();
  }, [adminVenues]);

  // Fetch previous broadcasts for each venue
  useEffect(() => {
    const fetchBroadcasts = async () => {
      if (!adminVenues.length) return;
      const venueIds = adminVenues.map(v => v.venue_id);
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('type', 'venue_broadcast')
        .or(venueIds.map(id => `metadata->>venue_id.eq.${id}`).join(','))
        .order('created_at', { ascending: false });
      if (!error && data) {
        // Group by venue_id
        const grouped: Record<string, any[]> = {};
        data.forEach((n: any) => {
          const venueId = n.metadata?.venue_id;
          if (!venueId) return;
          if (!grouped[venueId]) grouped[venueId] = [];
          grouped[venueId].push(n);
        });
        setBroadcastHistory(grouped);
      }
    };
    fetchBroadcasts();
  }, [adminVenues]);

  const handleSendBroadcast = async () => {
    if (!broadcastTitle || !broadcastMessage || !broadcastModal.venueId) return;
    setBroadcastLoading(true);

    try {
      // Check if venue has subscribers
      const { data: subs, error: subsError } = await supabase
        .from('venue_subscriptions')
        .select('user_id')
        .eq('venue_id', broadcastModal.venueId);

      if (subsError) throw subsError;
      if (!subs || subs.length === 0) {
        toast.error('No subscribers found for this venue');
        return;
      }

      // Create notification template using the new function
      const { data: templateId, error: templateError } = await supabase.rpc('create_venue_notification' as any, {
        p_venue_id: broadcastModal.venueId,
        p_title: broadcastTitle,
        p_message: broadcastMessage,
        p_created_by: user?.id,
        p_metadata: { venue_id: broadcastModal.venueId }
      });

      if (templateError) throw templateError;

      toast.success(`Notification sent for approval! Will be delivered to ${subs.length} subscribers once approved.`);
      setBroadcastModal({ open: false, venueId: null });
      setBroadcastTitle('');
      setBroadcastMessage('');
    } catch (error) {
      console.error('Error sending broadcast:', error);
      toast.error('Failed to send broadcast');
    } finally {
      setBroadcastLoading(false);
    }
  };

  // 🔄 REAL-TIME UPDATES: Handle live dashboard metrics updates
  const handleRealTimeMetricsUpdate = (metrics: RealTimeDashboardMetrics) => {
    // Update dashboard stats with real-time data
    setStats(prev => ({
      ...prev,
      todayBookings: metrics.todayBookings,
      pendingBookings: metrics.pendingBookings,
      monthlyRevenue: metrics.todaysRevenue
    }));

    setTodaysRevenue(metrics.todaysRevenue);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  useEffect(() => {
    if (!user) return;
    // Listen for updates to notifications for this admin's venues
    const channel = supabase.channel('admin_broadcast_approval')
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'notifications',
        filter: 'type=eq.venue_broadcast'
      }, async (payload) => {
        const notif = payload.new;
        // Only notify if this admin sent the broadcast (by venue ownership)
        if (notif.approved === true && notif.metadata && notif.metadata.venue_id && adminVenues.some(v => v.venue_id === notif.metadata.venue_id)) {
          // Only show once per notification
          if (!approvedBroadcastIdsRef.current.has(notif.id)) {
            approvedBroadcastIdsRef.current.add(notif.id);
            toast.success('Your broadcast message has been approved and sent to your subscribers!');
          }
        }
      })
      .subscribe();
    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, adminVenues]);

  const downloadRevenueReport = async () => {
    if (!customStartDate || !customEndDate || !user?.id) {
      toast.error('Please select both start and end dates');
      return;
    }

    setDownloadingReport(true);

    try {
      // Format dates for the query
      const startDate = typeof customStartDate === 'string' ? new Date(customStartDate) : customStartDate;
      const endDate = typeof customEndDate === 'string' ? new Date(customEndDate) : customEndDate;
      const startDateStr = format(startDate, 'yyyy-MM-dd');
      const endDateStr = format(endDate, 'yyyy-MM-dd');

      // PHASE 3: Get bookings including cancelled bookings for Excel export
      console.log('🔄 Mobile - Using RPC function to fetch booking data with coupons including cancelled...');
      const { data: rpcData, error } = await supabase
        .rpc('get_bookings_with_coupons_including_cancelled', {
          start_date: startDateStr,
          end_date: endDateStr
        });

      if (error) throw error;

      // Transform RPC data to match expected structure
      const bookingsData = rpcData?.map(booking => ({
        ...booking,
        court: booking.court_data,
        coupon_usage: booking.coupon_data,
        cancellation_data: booking.cancellation_data // PHASE 3: Include cancellation data
      })) || [];

      // Filter by admin venues and selected venue for revenue analytics
      const filteredBookings = (bookingsData || []).filter(booking => {
        // First filter by user role permissions
        if (userRole !== 'super_admin' && !adminVenues.some(v => v.venue_id === booking.court?.venue?.id)) {
          return false;
        }

        // Then filter by selected venue for revenue analytics
        if (selectedVenueForRevenue !== 'all') {
          return booking.court?.venue?.id === selectedVenueForRevenue;
        }

        return true;
      });

      // Get unique user IDs
      const userIds = [...new Set(filteredBookings.map(booking => booking.user_id).filter(Boolean))];

      // Batch fetch profiles
      let profilesMap: Record<string, any> = {};
      if (userIds.length > 0) {
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, full_name, phone, email')
          .in('id', userIds);

        if (!profilesError && profiles) {
          profilesMap = profiles.reduce((acc, profile) => {
            acc[profile.id] = profile;
            return acc;
          }, {} as Record<string, any>);
        }
      }

      // Process booking data for mobile dashboard

      // PHASE 3: Prepare Excel data including cancelled bookings
      const excelData = filteredBookings.map(booking => {
        // Handle coupon_usage which can be null or an array
        const couponUsageArray = (booking as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const hasCoupon = !!couponUsage;

        // PHASE 3: Check if booking is cancelled
        const isCancelled = booking.status === 'cancelled';
        const cancellationData = (booking as any).cancellation_data;

        console.log('🔍 Mobile - Processing booking:', booking.id, 'status:', booking.status, 'isCancelled:', isCancelled);

        // PHASE 1: OPTION B CALCULATION - Platform fee on original amount, venue bears coupon cost
        const totalPrice = Number(booking.total_price) || 0; // What user actually paid
        const originalAmount = hasCoupon ? (Number(couponUsage.original_price) || 0) : totalPrice;
        const discountAmount = hasCoupon ? (Number(couponUsage.discount_applied) || 0) : 0;
        const finalAmount = hasCoupon ? (Number(couponUsage.final_price) || 0) : totalPrice;

        const feePercent = booking.court?.venue?.platform_fee_percentage ?? 5;
        // CRITICAL FIX: Platform fee calculated on ORIGINAL amount (Option B)
        const platformFee = originalAmount * (feePercent / 100);
        // Net settlement: What venue actually receives = User payment - Platform fee (on original)
        const netSettlement = totalPrice - platformFee;

        const profile = profilesMap[booking.user_id] || {};

        return {
          'Booking Date': booking.booking_date,
          'Time Slot': `${booking.start_time} - ${booking.end_time}`,
          'Venue': booking.court?.venue?.name || 'N/A',
          'Court': booking.court?.name || 'N/A',
          'Customer Name': profile.full_name || 'N/A',
          'Customer Email': getDisplayEmail(profile.email) || 'N/A',
          'Customer Phone': profile.phone || 'N/A',
          'Coupon Applied': hasCoupon ? 'YES' : 'NO',
          'Coupon Code': hasCoupon ? (couponUsage.coupon?.code || 'N/A') : 'N/A',
          // PHASE 3: Mark cancelled booking amounts as "CANCELLED"
          'Original Amount (₹)': isCancelled ? 'CANCELLED' : originalAmount,
          'Discount Amount (₹)': isCancelled ? 'CANCELLED' : discountAmount,
          'Final Amount (₹)': isCancelled ? 'CANCELLED' : finalAmount,
          'User Payment (₹)': isCancelled ? 'CANCELLED' : totalPrice.toFixed(2),
          'Platform Fee (₹)': isCancelled ? 'CANCELLED' : platformFee.toFixed(2),
          'Net Settlement (₹)': isCancelled ? 'CANCELLED' : netSettlement.toFixed(2),
          'Payment Method': booking.payment_method || 'N/A',
          'Payment Status': booking.status,
          'Booking Reference': booking.booking_reference || 'N/A',
          'Created At': new Date(booking.created_at).toLocaleString(),
          'Platform Fee %': feePercent
        };
      });

      // PHASE 3: Separate confirmed/completed bookings from cancelled bookings
      const confirmedBookings = filteredBookings.filter(b => b.status === 'confirmed' || b.status === 'completed');
      const cancelledBookings = filteredBookings.filter(b => b.status === 'cancelled');

      // PHASE 1: OPTION B SUMMARY CALCULATIONS (Only for confirmed/completed bookings)
      // Gross revenue = sum of original amounts (before discounts) - EXCLUDES CANCELLED
      const totalGrossRevenue = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        return sum + originalAmount;
      }, 0);

      // Platform fee calculated on original amounts (Option B) - EXCLUDES CANCELLED
      const totalPlatformFee = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        return sum + (originalAmount * (feePercent / 100));
      }, 0);

      // TDS calculated on platform fees (online bookings only) - EXCLUDES CANCELLED
      const totalTdsAmount = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = b.payment_method === 'online' ? (platformFee * (tdsRate / 100)) : 0;
        return sum + tdsAmount;
      }, 0);

      // Net settlement = what user paid - platform fee (on original) - TDS - EXCLUDES CANCELLED
      const totalNetSettlement = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const userPayment = Number(b.total_price) || 0;
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = b.payment_method === 'online' ? (platformFee * (tdsRate / 100)) : 0;
        return sum + (userPayment - platformFee - tdsAmount);
      }, 0);
      const avgFee = confirmedBookings.length > 0 ? (confirmedBookings.reduce((sum, b) => sum + (b.court?.venue?.platform_fee_percentage ?? 5), 0) / confirmedBookings.length) : 5;

      // Calculate coupon summary statistics - EXCLUDES CANCELLED
      const totalCouponBookings = confirmedBookings.filter(b => {
        const couponUsageArray = (b as any).coupon_usage;
        return couponUsageArray && Array.isArray(couponUsageArray) && couponUsageArray.length > 0;
      }).length;

      const totalDiscountGiven = confirmedBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        return sum + (couponUsage ? (Number(couponUsage.discount_applied) || 0) : 0);
      }, 0);

      // HUDLE APPROACH: Separate Online vs Offline Bookings for Excel Report (EXCLUDES CANCELLED)
      const onlineBookings = confirmedBookings.filter(b => b.payment_method === 'online');
      const offlineBookings = confirmedBookings.filter(b => b.payment_method === 'cash');

      // Online Bookings Calculations (Settlement-affecting)
      const onlineGrossRevenue = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        return sum + originalAmount;
      }, 0);

      const onlinePlatformFee = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        return sum + (originalAmount * (feePercent / 100));
      }, 0);

      const onlineTdsAmount = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = platformFee * (tdsRate / 100);
        return sum + tdsAmount;
      }, 0);

      const onlineNetSettlement = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const userPayment = Number(b.total_price) || 0;
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = platformFee * (tdsRate / 100);
        return sum + (userPayment - platformFee - tdsAmount);
      }, 0);

      // FIX: Set the correct Net Settlement value for display
      setOnlineNetRevenue(onlineNetSettlement);

      // Offline Bookings Calculations (Informational only)
      const offlineGrossRevenue = offlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        return sum + originalAmount;
      }, 0);

      const offlineNetAmount = offlineBookings.reduce((sum, b) => {
        const userPayment = Number(b.total_price) || 0;
        return sum + userPayment;
      }, 0);

      const summaryRows = [
        {},
        { 'Booking Date': 'SUMMARY' },
        { 'Booking Date': 'Total Bookings', 'Time Slot': filteredBookings.length },
        // PHASE 3: Add cancelled booking count
        { 'Booking Date': 'Cancelled Booking Count', 'Time Slot': cancelledBookings.length },
        { 'Booking Date': 'Confirmed/Completed Bookings', 'Time Slot': confirmedBookings.length },
        { 'Booking Date': 'Bookings with Coupons', 'Time Slot': totalCouponBookings },
        { 'Booking Date': 'Total Discount Given', 'Time Slot': `₹${totalDiscountGiven.toFixed(2)}` },
        { 'Booking Date': 'Gross Revenue', 'Time Slot': `₹${totalGrossRevenue.toFixed(2)}` },
        { 'Booking Date': 'Platform Fee', 'Time Slot': `₹${totalPlatformFee.toFixed(2)}` },
        { 'Booking Date': 'TDS Amount', 'Time Slot': `₹${totalTdsAmount.toFixed(2)}` },
        { 'Booking Date': 'Net Settlement (What Venue Receives)', 'Time Slot': `₹${totalNetSettlement.toFixed(2)}` },
        {},
        // HUDLE APPROACH: Online vs Offline Breakdown
        { 'Booking Date': '=== ONLINE BOOKINGS (Settlement-Affecting) ===', 'Time Slot': '' },
        { 'Booking Date': 'Online Bookings Count', 'Time Slot': onlineBookings.length },
        { 'Booking Date': 'Online Gross Revenue', 'Time Slot': `₹${onlineGrossRevenue.toFixed(2)}` },
        { 'Booking Date': 'Online Platform Fee', 'Time Slot': `₹${onlinePlatformFee.toFixed(2)}` },
        { 'Booking Date': 'Online TDS Amount', 'Time Slot': `₹${onlineTdsAmount.toFixed(2)}` },
        { 'Booking Date': 'Online Net Settlement', 'Time Slot': `₹${onlineNetSettlement.toFixed(2)}` },
        {},
        { 'Booking Date': '=== OFFLINE BOOKINGS (Informational Only) ===', 'Time Slot': '' },
        { 'Booking Date': 'Offline Bookings Count (Cash)', 'Time Slot': offlineBookings.length },
        { 'Booking Date': 'Offline Gross Revenue (Cash)', 'Time Slot': `₹${offlineGrossRevenue.toFixed(2)}` },
        { 'Booking Date': 'Offline Net Amount (Cash)', 'Time Slot': `₹${offlineNetAmount.toFixed(2)}` },
        { 'Booking Date': 'Note', 'Time Slot': 'Offline bookings are venue-managed and NOT part of Grid२Play settlements' },
        {},
        { 'Booking Date': 'Avg Platform Fee %', 'Time Slot': `${avgFee.toFixed(2)}%` },
        { 'Booking Date': 'Date Range', 'Time Slot': `${format(startDate, 'dd/MM/yyyy')} to ${format(endDate, 'dd/MM/yyyy')}` }
      ];

      // Combine data with summary rows
      const allData = [...excelData, ...summaryRows];

      // Create workbook and worksheet with enhanced mobile formatting
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(allData);

      // Apply Grid२Play mobile-optimized formatting
      if (!ws['!rows']) ws['!rows'] = [];
      if (!ws['!cols']) ws['!cols'] = [];

      // Set mobile-optimized row heights (25 units ≈ 48px touch targets)
      for (let i = 0; i < allData.length; i++) {
        if (!ws['!rows'][i]) ws['!rows'][i] = {};
        ws['!rows'][i].hpt = 25; // Mobile-friendly row height
      }

      // Set mobile-optimized column widths
      const columnCount = Object.keys(allData[0] || {}).length;
      for (let col = 0; col < columnCount; col++) {
        if (!ws['!cols'][col]) ws['!cols'][col] = {};
        ws['!cols'][col].wch = 15; // Mobile-friendly column width
      }

      // Freeze header row for better mobile scrolling
      ws['!freeze'] = { xSplit: 0, ySplit: 1 };

      XLSX.utils.book_append_sheet(wb, ws, 'Revenue Report');

      // Generate filename with date range
      const filename = `revenue_report_${startDateStr}_to_${endDateStr}.xlsx`;
      XLSX.writeFile(wb, filename);

      toast.success('Revenue report downloaded successfully!');
    } catch (error) {
      toast.error('Failed to download report');
    } finally {
      setDownloadingReport(false);
    }
  };

  // Define quickLinks inside the component to access userRole
  const quickLinks = [
    {
      title: 'Analytics',
      path: '/admin/analytics-mobile',
      icon: <BarChart className="w-6 h-6 text-emerald-400" />,
      desc: 'View stats & trends',
      color: 'from-emerald-600 to-emerald-500',
      category: 'insights'
    },
    {
      title: 'Earnings',
      path: '/admin/earnings-mobile',
      icon: <DollarSign className="w-6 h-6 text-green-400" />,
      desc: 'Daily earnings & settlements',
      color: 'from-green-600 to-green-500',
      category: 'insights'
    },
    {
      title: 'Bookings',
      path: '/admin/bookings-mobile',
      icon: <Calendar className="w-6 h-6 text-blue-400" />,
      desc: 'Manage all bookings',
      color: 'from-blue-500 to-blue-400',
      category: 'operations'
    },
    {
      title: 'Venues',
      path: '/admin/venues-mobile',
      icon: <Map className="w-6 h-6 text-purple-400" />,
      desc: 'Edit your venues',
      color: 'from-purple-500 to-purple-400',
      category: 'management'
    },

    // Add FAQ management and refunds for super_admin only
    ...(userRole === 'super_admin' ? [
      {
        title: 'FAQ Management',
        path: '/admin/faq-management',
        icon: <HelpCircle className="w-6 h-6 text-indigo-400" />,
        desc: 'Manage venue FAQs',
        color: 'from-indigo-500 to-indigo-400',
        category: 'customer'
      },
      {
        title: 'Refunds & Cancellations',
        path: '/admin/refunds-cancellations-mobile',
        icon: <DollarSign className="w-6 h-6 text-green-400" />,
        desc: 'Manage refunds and cancellations',
        color: 'from-green-500 to-green-400',
        category: 'financial'
      },
      {
        title: 'Super Slot Manager',
        path: '/admin/super-slot-management',
        icon: <Ban className="w-6 h-6 text-red-400" />,
        desc: 'Advanced slot blocking',
        color: 'from-red-600 to-red-500',
        category: 'super_admin'
      }
    ] : [])
  ];

  // Add logging for date changes
  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = e.target.value ? parseISO(e.target.value) : null;
    setCustomStartDate(newDate);
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = e.target.value ? parseISO(e.target.value) : null;
    setCustomEndDate(newDate);
  };

  // FIX: Function to calculate Net Settlement for mobile
  const calculateNetSettlementForMobile = async (startDateStr: string, endDateStr: string) => {
    try {
      const { data: rpcData, error } = await supabase
        .rpc('get_bookings_with_coupons_including_cancelled', {
          start_date: startDateStr,
          end_date: endDateStr
        });

      if (error) throw error;

      // Transform and filter booking data
      const bookingsData = rpcData?.map(booking => ({
        ...booking,
        court: booking.court_data,
        coupon_usage: booking.coupon_data,
      })) || [];

      // Filter by admin venues and selected venue
      const filteredBookings = bookingsData.filter(booking => {
        if (userRole !== 'super_admin' && !adminVenues.some(v => v.venue_id === booking.court?.venue?.id)) {
          return false;
        }
        if (selectedVenueForRevenue !== 'all' && booking.court?.venue?.id !== selectedVenueForRevenue) {
          return false;
        }
        return true;
      });

      // Calculate Net Settlement for online bookings (confirmed/completed only)
      const confirmedBookings = filteredBookings.filter(b => b.status === 'confirmed' || b.status === 'completed');
      const onlineBookings = confirmedBookings.filter(b => b.payment_method === 'online');

      const calculatedNetSettlement = onlineBookings.reduce((sum, b) => {
        const couponUsageArray = (b as any).coupon_usage;
        const couponUsage = couponUsageArray && Array.isArray(couponUsageArray) ? couponUsageArray[0] : null;
        const originalAmount = couponUsage ? (Number(couponUsage.original_price) || 0) : (Number(b.total_price) || 0);
        const userPayment = Number(b.total_price) || 0;
        const feePercent = b.court?.venue?.platform_fee_percentage ?? 5;
        const tdsRate = b.court?.venue?.tds_rate ?? 1.0;
        const platformFee = originalAmount * (feePercent / 100);
        const tdsAmount = platformFee * (tdsRate / 100);
        return sum + (userPayment - platformFee - tdsAmount);
      }, 0);

      setOnlineNetRevenue(calculatedNetSettlement);
    } catch (error) {
      console.error('Error calculating net settlement for mobile:', error);
      setOnlineNetRevenue(0);
    }
  };

  // Custom revenue calculation with improved error handling and state management
  useEffect(() => {
    let isSubscribed = true;

    const calculateCustomRevenue = async () => {
      if (!customStartDate || !customEndDate || !user?.id) {
        if (isSubscribed) {
          setCustomRevenue(0);
          setOnlineRevenue(0);
          setOfflineRevenue(0);
          setOnlineBookings(0);
          setOfflineBookings(0);
        }
        return;
      }

      try {
        const startDateStr = format(customStartDate, 'yyyy-MM-dd');
        const endDateStr = format(customEndDate, 'yyyy-MM-dd');

        // PHASE 2: Use enhanced RPC function with online/offline separation
        const { data: revenueData, error } = await supabase
          .rpc('get_custom_revenue_with_option_b', {
            admin_user_id: user.id,
            start_date: startDateStr,
            end_date: endDateStr,
            filter_venue_id: selectedVenueForRevenue === 'all' ? null : selectedVenueForRevenue
          });

        if (error) throw error;

        console.log('🔍 Mobile Phase 2 Revenue Data:', revenueData);

        if (isSubscribed) {
          // Set revenue values with online/offline separation and TDS
          setCustomRevenue(revenueData?.grossRevenue || 0);
          setOnlineRevenue(revenueData?.onlineGrossRevenue || 0);
          setOfflineRevenue(revenueData?.offlineGrossRevenue || 0);
          setOnlineBookings(revenueData?.onlineBookings || 0);
          setOfflineBookings(revenueData?.offlineBookings || 0);
          setOnlineTdsAmount(revenueData?.onlineTdsAmount || 0);
          setTotalTdsAmount(revenueData?.tdsAmount || 0);

          // FIX: Calculate Net Settlement from actual booking data
          // We need to fetch and calculate this separately since RPC doesn't provide it
          calculateNetSettlementForMobile(startDateStr, endDateStr);
        }
      } catch (error) {
        console.error('Error calculating custom revenue:', error);
        if (isSubscribed) {
          setCustomRevenue(0);
          setOnlineRevenue(0);
          setOfflineRevenue(0);
          setOnlineBookings(0);
          setOfflineBookings(0);
        }
      }
    };

    // Debounce the calculation slightly to avoid rapid recalculation
    const timeoutId = setTimeout(calculateCustomRevenue, 100);

    return () => {
      isSubscribed = false;
      clearTimeout(timeoutId);
    };
  }, [customStartDate, customEndDate, user?.id, userRole, adminVenues, selectedVenueForRevenue]);



  // PHASE 4: HUDLE APPROACH - Separate Online vs Offline Report Sections (Mobile)
  const RevenueDisplay = () => {
    const safeOnlineRevenue = Number(onlineRevenue) || 0;
    const safeOfflineRevenue = Number(offlineRevenue) || 0;
    // Find the first admin venue's fee, fallback to 5
    const feePercent = venuesWithStats[0]?.platform_fee_percentage ?? 5;

    return (
      <div className="space-y-3">
        {/* Online Bookings Section (Settlement-Affecting) */}
        <div className="bg-gradient-to-r from-emerald-100 to-emerald-50 rounded-lg p-3 border border-emerald-300">
          <div className="flex flex-col gap-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <h5 className="text-sm font-semibold text-emerald-800">Online Bookings (Settlement-Affecting)</h5>
              </div>
              <Banknote className="h-4 w-4 text-emerald-600" />
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div className="bg-white/70 rounded p-2 border border-emerald-200">
                <div className="text-sm font-bold text-emerald-800">₹{safeOnlineRevenue.toFixed(0)}</div>
                <div className="text-xs text-emerald-600">Gross Revenue</div>
              </div>
              <div className="bg-white/70 rounded p-2 border border-emerald-200">
                <div className="text-sm font-bold text-emerald-700">{onlineBookings || 0}</div>
                <div className="text-xs text-emerald-600">Bookings</div>
              </div>
              <div className="bg-white/70 rounded p-2 border border-orange-200">
                <div className="text-sm font-bold text-orange-700">₹{(onlineTdsAmount || 0).toFixed(2)}</div>
                <div className="text-xs text-orange-600">TDS Amount</div>
              </div>
              <div className="bg-white/70 rounded p-2 border border-emerald-200">
                <div className="text-sm font-bold text-emerald-600">₹{(onlineNetRevenue || 0).toFixed(0)}</div>
                <div className="text-xs text-emerald-600">Final Settlement</div>
              </div>
            </div>

            <div className="text-xs text-emerald-600 bg-emerald-50 rounded p-2 border border-emerald-200">
              ✅ <strong>Included in settlements</strong> - Affects your payouts
            </div>
          </div>
        </div>

        {/* Offline Bookings Section (Informational Only) */}
        <div className="bg-gradient-to-r from-gray-100 to-gray-50 rounded-lg p-3 border border-gray-300">
          <div className="flex flex-col gap-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                <h5 className="text-sm font-semibold text-gray-800">Offline Bookings (Informational Only)</h5>
              </div>
              <Banknote className="h-4 w-4 text-gray-600" />
            </div>

            <div className="grid grid-cols-3 gap-2">
              <div className="bg-white/70 rounded p-2 border border-gray-200">
                <div className="text-sm font-bold text-gray-800">₹{safeOfflineRevenue.toFixed(0)}</div>
                <div className="text-xs text-gray-600">Gross Revenue</div>
              </div>
              <div className="bg-white/70 rounded p-2 border border-gray-200">
                <div className="text-sm font-bold text-gray-700">{offlineBookings || 0}</div>
                <div className="text-xs text-gray-600">Bookings</div>
              </div>
              <div className="bg-white/70 rounded p-2 border border-gray-200">
                <div className="text-sm font-bold text-gray-600">₹{safeOfflineRevenue.toFixed(0)}</div>
                <div className="text-xs text-gray-600">Cash Amount</div>
              </div>
            </div>

            <div className="text-xs text-gray-600 bg-gray-50 rounded p-2 border border-gray-200">
              ℹ️ <strong>NOT in settlements</strong> - Venue-managed only
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-black pb-20">
      {/* New Header - Similar to normal user mobile view */}
      <header className={`sticky top-0 z-10 transition-all duration-300 ${
        scrolled 
          ? 'bg-emerald-600/95 backdrop-blur-md shadow-lg' 
          : 'bg-gradient-to-r from-emerald-600 to-emerald-700'
      }`}>
        <div className="flex items-center justify-between px-4 py-4">
          <div className="flex items-center">
            <h1 className={`text-2xl font-bold transition-colors duration-300 ${
              scrolled ? 'text-white' : 'text-white'
            } tracking-wide`}>
              Admin Panel
            </h1>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-end">
              <span className={`text-sm font-medium truncate max-w-[100px] transition-colors duration-300 ${
                scrolled ? 'text-white' : 'text-white'
              }`}>
                {getDisplayName(user?.user_metadata?.full_name, user?.email) || 'Admin'}
              </span>
              <span className={`text-xs transition-colors duration-300 ${
                scrolled ? 'text-emerald-100' : 'text-emerald-100'
              }`}>
                Management Dashboard
              </span>
            </div>

            {/* Support Button for Mobile Admin */}
            <SupportButton variant="admin" className="scale-90" />

            <button
              onClick={handleLogout}
              className={`p-2 rounded-full transition-all duration-300 ${
                scrolled
                  ? 'bg-white/20 hover:bg-white/30'
                  : 'bg-white/20 hover:bg-white/30'
              }`}
            >
              <LogOut className="w-5 h-5 text-white" />
            </button>
          </div>
        </div>
      </header>

      {/* Modern Welcome Section */}
      <section className="px-4 pt-6 pb-4">
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-800/50 via-slate-700/30 to-emerald-900/20 backdrop-blur-sm border border-emerald-500/20 shadow-2xl mb-4">
          {/* Background decorative elements */}
          <div className="absolute -top-3 -right-3 w-16 h-16 bg-emerald-500/10 rounded-full blur-xl"></div>
          <div className="absolute -bottom-3 -left-3 w-12 h-12 bg-emerald-400/10 rounded-full blur-lg"></div>

          {/* Content */}
          <div className="relative z-10 p-5">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 border border-emerald-500/30 flex items-center justify-center backdrop-blur-sm">
                <span className="text-lg">👋</span>
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold bg-gradient-to-r from-white to-emerald-100 bg-clip-text text-transparent">
                  Welcome back, {user?.user_metadata?.full_name?.split(' ')[0] || 'Admin'}!
                </h2>
                <p className="text-emerald-200/90 text-sm mt-0.5">
                  Manage your Grid२Play venues efficiently
                </p>
              </div>
            </div>

            {/* Quick status indicator */}
            <div className="flex items-center gap-4 mt-4 pt-3 border-t border-emerald-500/20">
              <div className="flex items-center gap-2 text-emerald-300">
                <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse"></div>
                <span className="text-xs font-medium">Online</span>
              </div>
              <div className="text-emerald-200/70 text-xs">
                {new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>

          {/* Shine effect */}
          <div className="absolute inset-0 opacity-0 active:opacity-100 transition-opacity duration-300">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 translate-x-[-100%] active:translate-x-[200%] transition-transform duration-500"></div>
          </div>
        </div>
      </section>

      {/* Key Metrics Dashboard */}
      <section className="px-4 mb-6">
        <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          Today's Performance
        </h3>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-gradient-to-r from-slate-800/60 to-slate-700/40 backdrop-blur-sm rounded-2xl border border-emerald-500/20">
            <TabsTrigger value="overview" className="text-xs py-2 rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-emerald-200 transition-all duration-300">Overview</TabsTrigger>
            <TabsTrigger value="bookings" className="text-xs py-2 rounded-xl data-[state=active]:bg-emerald-500 data-[state=active]:text-white data-[state=active]:shadow-lg text-emerald-200 transition-all duration-300">Bookings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="mt-3">
            <div className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-500/20 shadow-xl">
              {stats.isLoading ? (
                <div className="flex justify-center items-center py-6">
                  <Loader2 className="h-8 w-8 animate-spin text-emerald-500" />
                </div>
              ) : (
                <div className="grid grid-cols-3 gap-3">
                  <Link to="/admin/bookings-mobile" className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-3 border border-emerald-200/50 hover:shadow-md transition-all">
                    <div className="text-2xl font-bold text-emerald-700">{stats.todayBookings}</div>
                    <div className="text-xs text-emerald-600 font-medium">Today's Bookings</div>
                  </Link>
                  <Link to="/admin/reviews-mobile" className="bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg p-3 border border-amber-200/50 hover:shadow-md transition-all">
                    <div className="text-2xl font-bold text-amber-700">{stats.averageRating}</div>
                    <div className="text-xs text-amber-600 font-medium">Avg Rating</div>
                  </Link>
                  <Link to="/admin/analytics-mobile" className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-3 border border-blue-200/50 hover:shadow-md transition-all">
                    <div className="text-2xl font-bold text-blue-700">{stats.occupancyRate}%</div>
                    <div className="text-xs text-blue-600 font-medium">Occupancy</div>
                  </Link>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="bookings" className="mt-3">
            <div className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-500/20 shadow-xl">
              {stats.isLoading ? (
                <div className="flex justify-center items-center py-6">
                  <Loader2 className="h-8 w-8 animate-spin text-emerald-500" />
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-3">
                  <Link to="/admin/bookings-mobile" className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-3 border border-orange-200/50 hover:shadow-md transition-all">
                    <Clock className="h-5 w-5 mb-2 text-orange-600" />
                    <div className="text-xl font-bold text-orange-700">{stats.pendingBookings}</div>
                    <div className="text-xs text-orange-600 font-medium">Pending Approval</div>
                  </Link>
                  <Link to="/admin/bookings-mobile" className="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-3 border border-emerald-200/50 hover:shadow-md transition-all">
                    <Calendar className="h-5 w-5 mb-2 text-emerald-600" />
                    <div className="text-xl font-bold text-emerald-700">{stats.upcomingBookings}</div>
                    <div className="text-xs text-emerald-600 font-medium">Upcoming (7 days)</div>
                  </Link>
                </div>
              )}
            </div>
          </TabsContent>
          

        </Tabs>
      </section>

      {/* Management Tools - Categorized */}
      <section className="px-4 mb-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <TrendingUp className="w-5 h-5 mr-2" />
          Management Tools
        </h3>
        
        {/* Business Insights */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-emerald-300 mb-3 uppercase tracking-wider">📊 Business Insights</h4>
          <div className="grid grid-cols-2 gap-3">
            {quickLinks.filter(link => link.category === 'insights').map(link => (
              <Link
                to={link.path}
                key={link.title}
                className={`rounded-xl shadow-sm bg-gradient-to-br ${link.color} p-4 flex flex-col items-start transition-all hover:shadow-md active:scale-95 relative overflow-hidden group border border-white/20`}
              >
                <div className="mb-2 p-2 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                <div className="text-white font-semibold text-base mb-1">{link.title}</div>
                <div className="text-xs text-white/90 leading-tight">{link.desc}</div>
                <ChevronRight className="absolute bottom-3 right-3 w-4 h-4 text-white/70" />
              </Link>
            ))}
          </div>
        </div>

        {/* Daily Operations */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-emerald-300 mb-3 uppercase tracking-wider">⚡ Daily Operations</h4>
          <div className="grid grid-cols-2 gap-3">
            {quickLinks.filter(link => link.category === 'operations').map(link => (
              <Link
                to={link.path}
                key={link.title}
                className={`rounded-xl shadow-sm bg-gradient-to-br ${link.color} p-4 flex flex-col items-start transition-all hover:shadow-md active:scale-95 relative overflow-hidden group border border-white/20`}
              >
                <div className="mb-2 p-2 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                <div className="text-white font-semibold text-base mb-1">{link.title}</div>
                <div className="text-xs text-white/90 leading-tight">{link.desc}</div>
                <ChevronRight className="absolute bottom-3 right-3 w-4 h-4 text-white/70" />
              </Link>
            ))}
            
            {/* Quick Action Tools */}
            <Link to="/admin/book-for-customer-mobile" className="rounded-xl shadow-sm bg-gradient-to-br from-cyan-500 to-cyan-400 p-4 flex flex-col items-start transition-all hover:shadow-md active:scale-95 relative overflow-hidden group border border-white/20">
              <div className="mb-2 p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                <Calendar className="w-6 h-6 text-cyan-400" />
              </div>
              <div className="text-white font-semibold text-base mb-1">Book for Customer</div>
              <div className="text-xs text-white/90 leading-tight">Create bookings</div>
              <ChevronRight className="absolute bottom-3 right-3 w-4 h-4 text-white/70" />
            </Link>
            
            <Link to="/admin/block-time-slots-mobile" className="rounded-xl shadow-sm bg-gradient-to-br from-rose-500 to-rose-400 p-4 flex flex-col items-start transition-all hover:shadow-md active:scale-95 relative overflow-hidden group border border-white/20">
              <div className="mb-2 p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                <Clock className="w-6 h-6 text-rose-400" />
              </div>
              <div className="text-white font-semibold text-base mb-1">Block Slots</div>
              <div className="text-xs text-white/90 leading-tight">Reserve time slots</div>
              <ChevronRight className="absolute bottom-3 right-3 w-4 h-4 text-white/70" />
            </Link>
          </div>
        </div>

        {/* Venue Management */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-emerald-300 mb-3 uppercase tracking-wider">🏟️ Venue Management</h4>
          <div className="grid grid-cols-2 gap-3">
            {quickLinks.filter(link => link.category === 'management').map(link => (
              <Link
                to={link.path}
                key={link.title}
                className={`rounded-xl shadow-sm bg-gradient-to-br ${link.color} p-4 flex flex-col items-start transition-all hover:shadow-md active:scale-95 relative overflow-hidden group border border-white/20`}
              >
                <div className="mb-2 p-2 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                <div className="text-white font-semibold text-base mb-1">{link.title}</div>
                <div className="text-xs text-white/90 leading-tight">{link.desc}</div>
                <ChevronRight className="absolute bottom-3 right-3 w-4 h-4 text-white/70" />
              </Link>
            ))}
          </div>
        </div>

        {/* Financial Management - Super Admin Only */}
        {userRole === 'super_admin' && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-emerald-300 mb-3 uppercase tracking-wider">💰 Financial Management</h4>
            <div className="grid grid-cols-1 gap-3">
              {quickLinks.filter(link => link.category === 'financial').map(link => (
                <Link
                  to={link.path}
                  key={link.title}
                  className={`rounded-xl shadow-sm bg-gradient-to-r ${link.color} p-4 flex items-center transition-all hover:shadow-md active:scale-95 relative overflow-hidden group border border-white/20`}
                >
                  <div className="mr-3 p-2 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                  <div className="flex-1">
                    <div className="text-white font-semibold text-base">{link.title}</div>
                    <div className="text-xs text-white/90">{link.desc}</div>
                  </div>
                  <ChevronRight className="w-5 h-5 text-white/70" />
                </Link>
              ))}
            </div>
          </div>
        )}



        {/* Super Admin Tools - Super Admin Only */}
        {userRole === 'super_admin' && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-emerald-300 mb-3 uppercase tracking-wider">⚡ Super Admin Tools</h4>
            <div className="grid grid-cols-1 gap-3">
              {quickLinks.filter(link => link.category === 'super_admin').map(link => (
                <Link
                  to={link.path}
                  key={link.title}
                  className={`rounded-xl shadow-sm bg-gradient-to-r ${link.color} p-4 flex items-center transition-all hover:shadow-md active:scale-95 relative overflow-hidden group border border-white/20`}
                >
                  <div className="mr-3 p-2 bg-white/20 rounded-lg backdrop-blur-sm">{React.cloneElement(link.icon)}</div>
                  <div className="flex-1">
                    <div className="text-white font-semibold text-base">{link.title}</div>
                    <div className="text-xs text-white/90">{link.desc}</div>
                  </div>
                  <ChevronRight className="w-5 h-5 text-white/70" />
                </Link>
              ))}
            </div>
          </div>
        )}
      </section>

      {/* Revenue Analytics */}
      <section className="px-4 mb-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <Banknote className="w-5 h-5 mr-2" />
          Revenue Analytics
        </h3>
        
        {/* Custom Date Range Revenue */}
        <div className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-500/20 shadow-xl mb-4">
          <h4 className="text-base font-medium text-emerald-300 mb-3">Custom Date Range Analysis</h4>
          <div className="space-y-3">
            {/* Venue Selector with Confirmation */}
            <div className="space-y-2">
              <VenueSelector
                venues={venues.filter(venue =>
                  userRole === 'super_admin' ||
                  adminVenues.some(v => v.venue_id === venue.id)
                )}
                selectedVenueId={pendingVenueSelection}
                onVenueChange={handleVenueChange}
                userRole={userRole}
                showAllOption={userRole === 'super_admin' || venues.length > 1}
                variant="mobile"
                label="Select Venue"
                placeholder="Choose venue"
                className="mb-1"
              />

              {/* Confirmation Button */}
              {showVenueConfirmation && (
                <div className="space-y-2">
                  <button
                    onClick={confirmVenueSelection}
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm"
                  >
                    ✓ Confirm Venue Selection
                  </button>
                  <div className="text-xs text-emerald-600 bg-emerald-50 p-2 rounded border border-emerald-200">
                    Tap "Confirm" to apply venue filter and update the revenue report
                  </div>
                </div>
              )}
            </div>

            <div className="flex gap-2">
              <input
                type="date"
                value={customStartDate ? format(customStartDate, 'yyyy-MM-dd') : ''}
                onChange={handleStartDateChange}
                className="bg-emerald-50 text-emerald-800 rounded-lg px-3 py-2 text-sm border border-emerald-200 flex-1"
              />
              <span className="text-emerald-600 self-center text-sm">to</span>
              <input
                type="date"
                value={customEndDate ? format(customEndDate, 'yyyy-MM-dd') : ''}
                onChange={handleEndDateChange}
                className="bg-emerald-50 text-emerald-800 rounded-lg px-3 py-2 text-sm border border-emerald-200 flex-1"
              />
            </div>
            
            <RevenueDisplay />
            
            {/* Download Button */}
            {customStartDate && customEndDate && (
              <div className="pt-2 border-t border-emerald-200">
                <Button
                  onClick={downloadRevenueReport}
                  disabled={downloadingReport}
                  size="sm"
                  className="w-full bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white"
                >
                  {downloadingReport ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating Report...
                    </>
                  ) : (
                    <>
                      <Download className="w-4 h-4 mr-2" />
                      Download Revenue Report
                    </>
                  )}
                </Button>
                <p className="text-xs text-emerald-600 mt-2 text-center">
                  Excel sheet with detailed booking analytics & revenue calculations
                </p>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Performance Insights */}
      <section className="px-4 mb-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <Award className="w-5 h-5 mr-2" />
          Performance Insights
        </h3>
        
        <div className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-500/20 shadow-xl">
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-base font-medium text-emerald-300">Top Performing Courts</h4>
            <Link to="/admin/analytics-mobile" className="text-sm text-emerald-400 hover:text-emerald-300">View All</Link>
          </div>
          
          {courtDataLoading || stats.isLoading ? (
            <div className="flex justify-center items-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-emerald-500" />
            </div>
          ) : popularCourts.length === 0 ? (
            <div className="text-center py-4 text-emerald-600">
              <p>No booking data available</p>
            </div>
          ) : (
            <div className="space-y-3">
              {popularCourts.map((court, index) => (
                <div key={index} className="flex justify-between items-center bg-emerald-50 p-3 rounded-lg">
                  <span className="text-sm font-medium text-emerald-800">{court.court_name}</span>
                  <span className="text-sm font-bold text-emerald-600">{court.bookings_percentage}% utilized</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* 🔄 REAL-TIME UPDATES: Live Notifications Section */}
      {showRealTimeUpdates && user?.id && userRoleState && (
        <section className="px-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <Activity className="w-5 h-5 mr-2 animate-pulse" />
              Live Updates
            </h3>
            <button
              onClick={() => setShowRealTimeUpdates(!showRealTimeUpdates)}
              className="text-xs text-emerald-400 hover:text-emerald-300 px-2 py-1 rounded bg-emerald-900/50"
            >
              {showRealTimeUpdates ? 'Hide' : 'Show'}
            </button>
          </div>

          <div className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-500/20 shadow-xl">
            <RealTimeNotifications
              userId={user.id}
              userRole={userRoleState}
              adminVenues={adminVenues}
              selectedVenueId={selectedVenueForRevenue}
              onMetricsUpdate={handleRealTimeMetricsUpdate}
            />
          </div>
        </section>
      )}

      {/* Weather Widget - Enhanced Design */}
      {adminVenues[0] && (
        <section className="px-4 mb-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Eye className="w-5 h-5 mr-2" />
            Weather Conditions
          </h3>
          <WeatherWidget venueId={adminVenues[0].venue_id} />
        </section>
      )}
      
      {/* Venue Subscribers & Broadcasts Section */}
      <section className="px-4 mb-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <MessageCircle className="w-5 h-5 mr-2" />
          Customer Communications
        </h3>
        <div className="space-y-4">
          {adminVenues.map(v => (
            <div key={v.venue_id} className="bg-gradient-to-br from-slate-800/60 via-slate-700/40 to-slate-900/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-500/20 shadow-xl">
              <div className="flex flex-col space-y-3">
                <div>
                  <div className="text-white font-semibold text-lg">{venuesWithStats.find(venue => venue.id === v.venue_id)?.name || 'Venue'}</div>
                  <div className="text-sm text-emerald-300">Subscribers: <span className="text-emerald-400 font-bold">{venueSubscribers[v.venue_id] || 0}</span></div>
                </div>
                
                {/* Broadcast History */}
                <div>
                  <div className="text-sm text-emerald-300 mb-2 font-medium">Recent Broadcasts</div>
                  <div className="bg-emerald-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                    {(broadcastHistory[v.venue_id] || []).slice(0, 3).map((b) => (
                      <div key={b.id} className="text-xs border-b border-emerald-100 pb-2 mb-2 last:border-b-0 last:mb-0">
                        <div className="flex justify-between items-start">
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-emerald-800 truncate">{b.title}</div>
                            <div className="text-emerald-600 truncate">{b.message}</div>
                          </div>
                          <div className="ml-2 flex-shrink-0">
                            {b.approved ? (
                              <span className="text-green-600 text-xs font-medium">✓ Sent</span>
                            ) : (
                              <span className="text-yellow-600 text-xs font-medium">⏳ Pending</span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {(broadcastHistory[v.venue_id] || []).length === 0 && (
                      <div className="text-emerald-500 text-xs">No broadcasts yet</div>
                    )}
                  </div>
                </div>
                
                <Button
                  className="bg-emerald-600 hover:bg-emerald-700 text-white"
                  onClick={() => setBroadcastModal({ open: true, venueId: v.venue_id })}
                >
                  Send Notification
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        <Dialog open={broadcastModal.open} onOpenChange={open => setBroadcastModal({ open, venueId: open ? broadcastModal.venueId : null })}>
          <DialogContent className="bg-gray-900 border-gray-700 max-w-[95vw] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-white">Send Broadcast Notification</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              {/* Template Library - Compact for Mobile */}
              <NotificationTemplateLibrary
                onUseTemplate={(title, message) => {
                  setBroadcastTitle(title);
                  setBroadcastMessage(message);
                }}
                venueName={venuesWithStats.find(venue => venue.id === broadcastModal.venueId)?.name || ''}
              />

              {/* Custom Message Form */}
              <div className="space-y-3">
                <h4 className="text-white font-medium text-sm">Custom Message</h4>
                <input
                  className="w-full px-3 py-2 rounded-lg bg-gray-800 text-white border border-gray-600 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
                  placeholder="Title"
                  value={broadcastTitle}
                  onChange={e => setBroadcastTitle(e.target.value)}
                  maxLength={80}
                />
                <textarea
                  className="w-full px-3 py-2 rounded-lg bg-gray-800 text-white border border-gray-600 focus:border-emerald-500 focus:ring-1 focus:ring-emerald-500"
                  placeholder="Message"
                  value={broadcastMessage}
                  onChange={e => setBroadcastMessage(e.target.value)}
                  rows={3}
                  maxLength={300}
                />
              </div>
            </div>

            <DialogFooter>
              <Button
                onClick={handleSendBroadcast}
                disabled={broadcastLoading || !broadcastTitle || !broadcastMessage}
                className="bg-emerald-600 hover:bg-emerald-700 text-white"
              >
                {broadcastLoading ? 'Sending...' : 'Send for Approval'}
              </Button>
              <Button
                variant="outline"
                onClick={() => setBroadcastModal({ open: false, venueId: null })}
                className="border-gray-600 text-gray-300"
              >
                Cancel
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </section>
      
      {/* Footer */}
      <footer className="px-4 py-6 mt-8 text-center bg-black/50">
        <p className="text-sm text-emerald-400 font-medium">Grid2Play Admin Dashboard</p>
        <p className="text-xs text-emerald-500 mt-1">&copy; {new Date().getFullYear()} All rights reserved</p>
      </footer>
    </div>
  );
};

export default AdminHome_Mobile;
