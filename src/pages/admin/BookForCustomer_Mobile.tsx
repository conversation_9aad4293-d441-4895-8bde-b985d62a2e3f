
import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, ArrowLeft, User, Clock } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { toast } from '@/components/ui/use-toast';
import AdminBookingForm from '@/components/AdminBookingForm';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

interface TimeSlot {
  start_time: string;
  end_time: string;
  price: string;
  is_available: boolean;
  booking_type: 'court_based' | 'capacity_based';
  available_spots?: number;
  max_capacity?: number;
}

const BookForCustomer_Mobile: React.FC = () => {
  const { userRole } = useAuth();
  const [adminVenues, setAdminVenues] = useState<{ venue_id: string, venue_name: string, allow_cash_payments: boolean }[]>([]);
  const [selectedVenueId, setSelectedVenueId] = useState<string>('');
  const [selectedVenueName, setSelectedVenueName] = useState<string>('');
  const [allowCashPayments, setAllowCashPayments] = useState<boolean>(true);
  const [courts, setCourts] = useState<{ id: string; name: string; hourly_rate: number }[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedCourtId, setSelectedCourtId] = useState<string>('');
  const [selectedCourtName, setSelectedCourtName] = useState<string>('');
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [selectedSlotPrices, setSelectedSlotPrices] = useState<Record<string, number>>({});
  const [availableTimeSlots, setAvailableTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [lastRefresh, setLastRefresh] = useState<number>(Date.now());
  const navigate = useNavigate();

  // Time utility functions
  const timeToMinutes = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '';
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const parseFormattedTime = (formattedTime: string) => {
    const [time, ampm] = formattedTime.split(' ');
    const [hours, minutes] = time.split(':');
    let hour = parseInt(hours, 10);

    if (ampm === 'PM' && hour !== 12) {
      hour += 12;
    } else if (ampm === 'AM' && hour === 12) {
      hour = 0;
    }

    return `${hour.toString().padStart(2, '0')}:${minutes}`;
  };

  // Slot selection logic from BookingPage.tsx
  const areSlotsContinuous = (slots: string[]) => {
    if (slots.length <= 1) return true;

    const sortedSlots = slots.sort((a, b) => {
      const startTimeA = a.split(' - ')[0];
      const startTimeB = b.split(' - ')[0];
      return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
    });

    for (let i = 0; i < sortedSlots.length - 1; i++) {
      const currentEndTime = sortedSlots[i].split(' - ')[1];
      const nextStartTime = sortedSlots[i + 1].split(' - ')[0];

      if (currentEndTime !== nextStartTime) {
        return false;
      }
    }
    return true;
  };

  const wouldCreateValidSelection = (newSlot: string, currentSlots: string[]) => {
    if (currentSlots.length === 0) return true;
    const testSlots = [...currentSlots, newSlot];
    return areSlotsContinuous(testSlots);
  };

  const getSelectableSlots = (currentSlots: string[], availableSlots: TimeSlot[]) => {
    if (currentSlots.length === 0) {
      return availableSlots.map(slot => `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`);
    }

    const sortedCurrentSlots = currentSlots.sort((a, b) => {
      const startTimeA = a.split(' - ')[0];
      const startTimeB = b.split(' - ')[0];
      return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
    });

    const firstSlotStart = sortedCurrentSlots[0].split(' - ')[0];
    const lastSlotEnd = sortedCurrentSlots[sortedCurrentSlots.length - 1].split(' - ')[1];

    return availableSlots
      .map(slot => `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`)
      .filter(slotDisplay => {
        if (currentSlots.includes(slotDisplay)) return true;

        const slotStart = slotDisplay.split(' - ')[0];
        const slotEnd = slotDisplay.split(' - ')[1];

        return slotEnd === firstSlotStart || slotStart === lastSlotEnd;
      });
  };

  // Fetch admin venues on component mount
  useEffect(() => {
    const fetchAdminVenues = async () => {
      try {
        setLoading(true);
        // Get admin venues using RPC function
        const { data, error } = await supabase.rpc('get_admin_venues');
        
        if (error) {
          throw error;
        }
        
        if (data && data.length > 0) {
          console.log("Admin venues fetched:", data);
          setAdminVenues(data);
          // Select first venue by default
          setSelectedVenueId(data[0].venue_id);
          setSelectedVenueName(data[0].venue_name);
          setAllowCashPayments(data[0].allow_cash_payments !== false);
        } else {
          toast({
            title: "No venues",
            description: "You don't have access to any venues.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error fetching admin venues:', error);
        toast({
          title: "Error",
          description: "Failed to load your venues. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    if (userRole === 'admin' || userRole === 'super_admin') {
      fetchAdminVenues();
    }
  }, [userRole]);

  // Fetch courts when venue is selected
  useEffect(() => {
    const fetchCourts = async () => {
      if (!selectedVenueId) return;
      
      try {
        setLoading(true);
        console.log("Fetching courts for venue:", selectedVenueId);
        const { data, error } = await supabase
          .from('courts')
          .select('id, name, hourly_rate')
          .eq('venue_id', selectedVenueId)
          .eq('is_active', true);
        
        if (error) throw error;
        
        if (data && data.length > 0) {
          console.log("Courts fetched:", data);
          setCourts(data);
          // Select first court by default
          setSelectedCourtId(data[0].id);
          setSelectedCourtName(data[0].name);
        } else {
          setCourts([]);
          setSelectedCourtId('');
          setSelectedCourtName('');
          toast({
            title: "No courts",
            description: "No active courts found for this venue.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error fetching courts:', error);
        toast({
          title: "Error",
          description: "Failed to load courts. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchCourts();
  }, [selectedVenueId]);

  // Set up real-time subscription for bookings and blocked slots
  useEffect(() => {
    // Bookings channel subscription for real-time updates
    const bookingsChannel = supabase.channel('bookings_mobile_channel')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookings'
        },
        () => {
          // Force refresh of the availability widget when bookings change
          setLastRefresh(Date.now());
        }
      )
      .subscribe();

    // Blocked slots channel subscription for real-time updates
    const blockedSlotsChannel = supabase.channel('blocked_slots_mobile_channel')
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'blocked_slots'
        },
        () => {
          // Force refresh of the availability widget when blocked slots change
          setLastRefresh(Date.now());
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(bookingsChannel);
      supabase.removeChannel(blockedSlotsChannel);
    };
  }, []);

  // Handle court selection
  const handleCourtSelect = (courtId: string) => {
    const court = courts.find(c => c.id === courtId);
    if (court) {
      setSelectedCourtId(courtId);
      setSelectedCourtName(court.name);
      setSelectedSlots([]); // Reset selected slots when changing courts
      setSelectedSlotPrices({});
    }
  };

  // Enhanced slot selection logic from BookingPage.tsx
  const handleSlotClick = (slot: TimeSlot) => {
    if (!slot.is_available) return;

    const slotDisplay = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;
    const slotPrice = parseFloat(slot.price);

    // For capacity-based sports, only allow single slot selection
    if (slot.booking_type === 'capacity_based') {
      if (selectedSlots.includes(slotDisplay)) {
        setSelectedSlots([]);
        setSelectedSlotPrices({});
      } else {
        setSelectedSlots([slotDisplay]);
        setSelectedSlotPrices({ [slotDisplay]: slotPrice });
      }
      return;
    }

    // Enhanced logic for court-based sports with consecutive validation
    if (selectedSlots.includes(slotDisplay)) {
      const newSlots = selectedSlots.filter(s => s !== slotDisplay);
      const newSelectedSlotPrices = { ...selectedSlotPrices };
      delete newSelectedSlotPrices[slotDisplay];

      if (newSlots.length > 0 && !areSlotsContinuous(newSlots)) {
        toast({
          title: "Invalid selection",
          description: "Removing this slot would create a gap. Please select consecutive time slots only.",
          variant: "destructive",
        });
        return;
      }

      setSelectedSlots(newSlots);
      setSelectedSlotPrices(newSelectedSlotPrices);
    } else {
      // Regular single slot selection
      if (!wouldCreateValidSelection(slotDisplay, selectedSlots)) {
        toast({
          title: "Invalid selection",
          description: "Please select consecutive time slots only.",
          variant: "destructive",
        });
        return;
      }

      const updatedSlots = [...selectedSlots, slotDisplay];
      const sortedSlots = updatedSlots.sort((a, b) => {
        const startTimeA = a.split(' - ')[0];
        const startTimeB = b.split(' - ')[0];
        return timeToMinutes(startTimeA) - timeToMinutes(startTimeB);
      });

      setSelectedSlots(sortedSlots);
      setSelectedSlotPrices({
        ...selectedSlotPrices,
        [slotDisplay]: slotPrice
      });
    }
  };

  // Fetch availability for selected court and date
  const fetchAvailability = useCallback(async () => {
    if (!selectedCourtId || !selectedDate) return;

    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('get_unified_availability' as any, {
        p_court_id: selectedCourtId,
        p_date: format(selectedDate, 'yyyy-MM-dd')
      });

      if (error) throw error;

      const slotsWithPrice = (data as any)?.map((slot: {
        start_time: string;
        end_time: string;
        is_available: boolean;
        available_spots: number;
        max_capacity: number;
        price: string;
        booking_type: string;
      }) => ({
        ...slot,
        price: slot.price || '0',
        booking_type: slot.booking_type || 'court_based'
      })) || [];

      setAvailableTimeSlots(slotsWithPrice);
    } catch (error) {
      console.error('Error fetching availability:', error);
      toast({
        title: "Error",
        description: "Failed to load availability",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [selectedCourtId, selectedDate]);

  // Handle booking completion
  const handleBookingComplete = () => {
    setSelectedSlots([]);
    setSelectedSlotPrices({});
    setLastRefresh(Date.now()); // Force refresh after booking
    fetchAvailability(); // Refresh availability
  };

  // Fetch availability when court or date changes
  useEffect(() => {
    if (selectedCourtId && selectedDate) {
      fetchAvailability();
    }
  }, [selectedCourtId, selectedDate, lastRefresh, fetchAvailability]);

  // Handle manual refresh
  const handleManualRefresh = () => {
    setLastRefresh(Date.now());
  };

  // Handle venue change
  const handleVenueChange = (venueId: string) => {
    const venue = adminVenues.find(v => v.venue_id === venueId);
    if (venue) {
      setSelectedVenueId(venue.venue_id);
      setSelectedVenueName(venue.venue_name);
      setAllowCashPayments(venue.allow_cash_payments !== false);
      setSelectedSlots([]);
      setSelectedSlotPrices({});
      // Reset court selection when venue changes
      setSelectedCourtId('');
      setSelectedCourtName('');
    }
  };

  if (loading && adminVenues.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-black flex justify-center items-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-emerald-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-black pb-20">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-gradient-to-r from-slate-900/90 to-slate-800/90 backdrop-blur-md shadow-md border-b border-emerald-500/20">
        <div className="flex items-center px-4 py-4">
          <button 
            onClick={() => navigate('/admin/mobile-home')}
            className="mr-3 p-1.5 rounded-full bg-navy-800 hover:bg-navy-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-400" />
          </button>
          <h1 className="text-xl font-bold text-white">Book for Customer</h1>
        </div>
      </header>
    
      <div className="p-4">
        <div className="bg-navy-800/70 rounded-xl p-4 border border-navy-700/50 mb-4">      
          {/* Venue Selection */}
          {adminVenues.length > 0 && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-white mb-1">
                Select Venue
              </label>
              <Select 
                value={selectedVenueId} 
                onValueChange={(e) => handleVenueChange(e)}
              >
                <SelectTrigger className="w-full bg-navy-900 border-navy-700 text-white">
                  <SelectValue placeholder="Select venue" />
                </SelectTrigger>
                <SelectContent className="bg-navy-900 border-navy-700 text-white">
                  {adminVenues.map(venue => (
                    <SelectItem key={venue.venue_id} value={venue.venue_id} className="text-white hover:bg-navy-800">
                      {venue.venue_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {/* Date Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-white mb-1">
              Select Date
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start bg-navy-900 border-navy-700 text-white">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {format(selectedDate, 'PPP')}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => {
                    if (date) {
                      setSelectedDate(date);
                      setSelectedSlots([]);
                      setSelectedSlotPrices({});
                    }
                  }}
                  initialFocus
                  disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                />
              </PopoverContent>
            </Popover>
          </div>
          
          {/* Court Selection */}
          {courts.length > 0 && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-white mb-1">
                Select Court
              </label>
              <Select value={selectedCourtId} onValueChange={handleCourtSelect}>
                <SelectTrigger className="w-full bg-navy-900 border-navy-700 text-white">
                  <SelectValue placeholder="Select court" />
                </SelectTrigger>
                <SelectContent className="bg-navy-900 border-navy-700 text-white">
                  {courts.map(court => (
                    <SelectItem key={court.id} value={court.id} className="text-white hover:bg-navy-800">
                      {court.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {/* Manual refresh button */}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleManualRefresh}
            className="mb-4 w-full bg-navy-700 hover:bg-navy-600 text-white"
          >
            Refresh Availability
          </Button>
        </div>
        
        {/* Enhanced Slot Selection UI */}
        {selectedCourtId ? (
          <div className="bg-navy-800/70 rounded-xl p-4 border border-navy-700/50 mb-4">
            <h3 className="text-md font-medium mb-3 text-white">Available Time Slots</h3>

            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Clock className="h-8 w-8 animate-spin text-emerald-400" />
                <span className="ml-2 text-gray-400">Loading slots...</span>
              </div>
            ) : availableTimeSlots.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-400">No slots available for this date</p>
              </div>
            ) : (
              <>
                {/* Consecutive Slot Selection Info */}
                {availableTimeSlots.some(slot => slot.booking_type === 'court_based') && (
                  <div className="mb-4 p-3 bg-blue-900/20 rounded-lg border border-blue-800/30">
                    <div className="flex items-start gap-2">
                      <div className="w-4 h-4 bg-blue-500 rounded-full mt-0.5 flex-shrink-0"></div>
                      <div>
                        <h4 className="text-sm font-medium text-blue-300 mb-1">Consecutive Slot Selection</h4>
                        <p className="text-xs text-gray-400 mb-2">
                          You can select multiple time slots, but they must be consecutive (no gaps).
                        </p>
                        <p className="text-xs text-blue-300">
                          💡 <strong>Tip:</strong> Select consecutive slots for longer bookings
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Slot Grid */}
                <div className="grid grid-cols-1 gap-3">
                  {availableTimeSlots.map((slot, index) => {
                    const slotDisplay = `${formatTime(slot.start_time)} - ${formatTime(slot.end_time)}`;
                    const isSelected = selectedSlots.includes(slotDisplay);
                    const selectableSlots = getSelectableSlots(selectedSlots, availableTimeSlots);
                    const isSelectable = slot.is_available && (selectedSlots.length === 0 || selectableSlots.includes(slotDisplay));
                    const isDisabledByRule = slot.is_available && !isSelected && !isSelectable;

                    return (
                      <motion.button
                        key={`${slot.start_time}-${slot.end_time}`}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        whileHover={{ scale: isSelectable ? 1.02 : 1 }}
                        whileTap={{ scale: isSelectable ? 0.98 : 1 }}
                        disabled={!isSelectable}
                        onClick={() => handleSlotClick(slot)}
                        className={`
                          relative p-4 rounded-xl border-2 transition-all duration-200 text-left
                          min-h-[80px] flex flex-col justify-between
                          ${!slot.is_available
                            ? 'bg-red-900/20 border-red-800/50 text-red-300 cursor-not-allowed'
                            : isSelected
                              ? 'bg-emerald-600/20 border-emerald-500 text-emerald-100 shadow-lg shadow-emerald-900/20'
                              : isDisabledByRule
                                ? 'bg-gray-800/30 border-gray-700/50 text-gray-500 cursor-not-allowed opacity-50'
                                : 'bg-gray-800/50 border-gray-600/50 hover:border-emerald-500/50 hover:bg-gray-750/50 text-gray-200'
                          }
                          ${isSelected ? 'ring-2 ring-emerald-400/50' : ''}
                        `}
                      >
                        {/* Time and Price Row */}
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-semibold text-base">
                              {formatTime(slot.start_time)}
                            </div>
                            <div className="text-sm opacity-75">
                              to {formatTime(slot.end_time)}
                            </div>
                          </div>

                          <div className="text-right">
                            <div className="font-bold text-base">
                              ₹{parseFloat(slot.price).toFixed(0)}
                            </div>
                            {slot.booking_type === 'capacity_based' && (
                              <div className="text-xs opacity-75 flex items-center gap-1">
                                <User size={10} />
                                {slot.available_spots || 0}/{slot.max_capacity || 0}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Status Indicators */}
                        <div className="flex justify-between items-center mt-2">
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full transition-all ${
                              isSelected
                                ? 'bg-emerald-400 shadow-lg shadow-emerald-400/50'
                                : slot.is_available
                                  ? 'bg-gray-600 border border-gray-500'
                                  : 'bg-red-600'
                            }`} />

                            <span className="text-xs font-medium">
                              {!slot.is_available
                                ? 'Booked'
                                : isSelected
                                  ? 'Selected'
                                  : isDisabledByRule
                                    ? 'Not consecutive'
                                    : 'Available'
                              }
                            </span>
                          </div>

                          {slot.booking_type === 'capacity_based' && (
                            <div className="text-xs bg-blue-900/30 text-blue-300 px-2 py-1 rounded-full">
                              Capacity
                            </div>
                          )}
                        </div>
                      </motion.button>
                    );
                  })}
                </div>

                {/* Selected Slots Summary */}
                {selectedSlots.length > 0 && (
                  <div className="mt-4 p-3 bg-emerald-900/20 rounded-lg border border-emerald-700/30">
                    <h4 className="text-sm font-medium text-emerald-300 mb-2">Selected Slots</h4>
                    <div className="space-y-1">
                      {selectedSlots.map(slot => (
                        <div key={slot} className="flex justify-between text-xs">
                          <span className="text-gray-300">{slot}</span>
                          <span className="text-emerald-300">₹{selectedSlotPrices[slot]?.toFixed(0) || '0'}</span>
                        </div>
                      ))}
                    </div>
                    <div className="mt-2 pt-2 border-t border-emerald-700/30 flex justify-between text-sm font-medium">
                      <span className="text-emerald-300">Total</span>
                      <span className="text-emerald-300">
                        ₹{Object.values(selectedSlotPrices).reduce((sum, price) => sum + price, 0).toFixed(0)}
                      </span>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        ) : (
          <div className="bg-navy-800/70 rounded-xl p-4 border border-navy-700/50 mb-4">
            <p className="text-gray-400 text-center py-4">Select a court to view availability</p>
          </div>
        )}
        
        {/* Booking Form */}
        {selectedCourtId && selectedSlots.length > 0 ? (
          <div className="bg-navy-800/70 rounded-xl p-4 border border-navy-700/50">
            <h3 className="text-md font-medium mb-3 text-white">Create Booking</h3>
            <AdminBookingForm
              courtId={selectedCourtId}
              courtName={selectedCourtName}
              venueName={selectedVenueName}
              venueId={selectedVenueId}
              date={format(selectedDate, 'yyyy-MM-dd')}
              selectedSlot={{
                start_time: parseFormattedTime(selectedSlots[0].split(' - ')[0]),
                end_time: parseFormattedTime(selectedSlots[selectedSlots.length - 1].split(' - ')[1]),
                is_available: true
              }}
              totalPrice={Object.values(selectedSlotPrices).reduce((sum, price) => sum + price, 0)}
              onBookingComplete={handleBookingComplete}
              allowCashPayments={allowCashPayments}
            />
          </div>
        ) : (
          <div className="bg-navy-800/70 rounded-xl p-4 border border-navy-700/50">
            <p className="text-gray-400 text-center py-4">
              Select time slots to create a booking
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BookForCustomer_Mobile;
