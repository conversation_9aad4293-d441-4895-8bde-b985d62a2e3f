<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Grid२Play - Book Sports Venues Easily</title>
    <meta name="description" content="Grid२Play lets you book sports venues and courts with ease. Find, compare, and reserve your favorite sports spaces online." />
    <meta name="author" content="Grid२Play Team" />

    <meta property="og:title" content="Grid२Play - Book Sports Venues Easily" />
    <meta property="og:description" content="Book sports venues and courts with Grid२Play. Fast, easy, and reliable." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/favicon.ico" />

    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="Grid२Play - Book Sports Venues Easily" />
    <meta name="twitter:description" content="Book sports venues and courts with Grid२Play. Fast, easy, and reliable." />
    <meta name="twitter:image" content="/favicon.ico" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <!-- Safari-specific font loading and text rendering fixes -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
      /* Safari text rendering optimization */
      * {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        text-rendering: optimizeLegibility;
        -webkit-text-size-adjust: 100%;
      }

      /* Prevent FOUC (Flash of Unstyled Content) in Safari */
      .font-loading {
        visibility: hidden;
      }

      .fonts-loaded .font-loading {
        visibility: visible;
      }
    </style>
  </head>

  <body>
    <div id="root" class="font-loading"></div>

    <!-- Safari font loading optimization -->
    <script>
      // Detect Safari and apply font loading optimizations
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      if (isSafari) {
        // Add fonts-loaded class when fonts are ready
        if (document.fonts && document.fonts.ready) {
          document.fonts.ready.then(() => {
            document.documentElement.classList.add('fonts-loaded');
          });
        } else {
          // Fallback for older Safari versions
          setTimeout(() => {
            document.documentElement.classList.add('fonts-loaded');
          }, 100);
        }
      } else {
        // For non-Safari browsers, add class immediately
        document.documentElement.classList.add('fonts-loaded');
      }
    </script>

    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
